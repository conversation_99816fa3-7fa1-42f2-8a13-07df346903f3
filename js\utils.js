/**
 * Utility functions for the banking application
 * Contains shared functions for validation, formatting, and async operations
 */

/**
 * Validates transaction amounts
 * @param {number} amount - The amount to validate
 * @param {number} currentBalance - Current account balance (for withdrawal validation)
 * @param {string} type - Transaction type ('deposit' or 'withdraw')
 * @returns {Object} Validation result with isValid and message properties
 */
export function validateAmount(amount, currentBalance = 0, type = 'deposit') {
    // Check if amount is a valid number
    if (isNaN(amount) || amount === null || amount === undefined) {
        return {
            isValid: false,
            message: 'Please enter a valid number'
        };
    }

    // Convert to number and check if positive
    const numAmount = parseFloat(amount);
    
    if (numAmount <= 0) {
        return {
            isValid: false,
            message: 'Amount must be greater than zero'
        };
    }

    // Check for reasonable decimal places (max 2)
    if (numAmount.toString().includes('.') && numAmount.toString().split('.')[1].length > 2) {
        return {
            isValid: false,
            message: 'Amount cannot have more than 2 decimal places'
        };
    }

    // Check maximum limits
    const MAX_TRANSACTION = 10000;
    if (numAmount > MAX_TRANSACTION) {
        return {
            isValid: false,
            message: `Maximum transaction limit is $${MAX_TRANSACTION.toLocaleString()}`
        };
    }

    // Additional validation for withdrawals
    if (type === 'withdraw') {
        if (numAmount > currentBalance) {
            return {
                isValid: false,
                message: 'Insufficient funds for this withdrawal'
            };
        }

        // Minimum balance requirement
        const MIN_BALANCE = 0;
        if (currentBalance - numAmount < MIN_BALANCE) {
            return {
                isValid: false,
                message: `Transaction would result in negative balance`
            };
        }
    }

    return {
        isValid: true,
        message: 'Valid amount'
    };
}

/**
 * Simulates backend API delay for realistic user experience
 * @param {number} ms - Milliseconds to delay (default: random between 1-3 seconds)
 * @returns {Promise} Promise that resolves after the specified delay
 */
export function delay(ms = null) {
    // If no specific delay provided, use random delay between 1-3 seconds
    const delayTime = ms || Math.floor(Math.random() * 2000) + 1000;
    
    return new Promise(resolve => {
        setTimeout(resolve, delayTime);
    });
}

/**
 * Formats currency amounts for display
 * @param {number} amount - The amount to format
 * @param {string} currency - Currency symbol (default: '$')
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount, currency = '$') {
    if (isNaN(amount)) return `${currency}0.00`;
    
    return `${currency}${parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })}`;
}

/**
 * Formats date and time for transaction history
 * @param {Date} date - Date object to format
 * @returns {string} Formatted date string
 */
export function formatDateTime(date = new Date()) {
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Generates unique transaction ID
 * @returns {string} Unique transaction ID
 */
export function generateTransactionId() {
    return 'TXN-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

/**
 * Determines balance status and color coding
 * @param {number} balance - Current balance
 * @returns {Object} Status object with level and text
 */
export function getBalanceStatus(balance) {
    if (balance < 100) {
        return {
            level: 'low',
            text: 'Low Balance',
            class: 'balance-low'
        };
    } else if (balance < 1000) {
        return {
            level: 'medium',
            text: 'Good Standing',
            class: 'balance-medium'
        };
    } else {
        return {
            level: 'high',
            text: 'Excellent Standing',
            class: 'balance-high'
        };
    }
}

/**
 * Shows loading animation with dots
 * @param {HTMLElement} element - Element to show loading animation in
 * @param {string} baseText - Base text to show (default: 'Processing')
 * @returns {number} Interval ID for clearing the animation
 */
export function showLoadingAnimation(element, baseText = 'Processing') {
    let dots = 0;
    
    const intervalId = setInterval(() => {
        dots = (dots + 1) % 4;
        const dotString = '.'.repeat(dots);
        element.textContent = baseText + dotString;
    }, 500);
    
    return intervalId;
}

/**
 * Clears loading animation
 * @param {number} intervalId - Interval ID returned by showLoadingAnimation
 * @param {HTMLElement} element - Element to reset
 * @param {string} finalText - Final text to display
 */
export function clearLoadingAnimation(intervalId, element, finalText) {
    clearInterval(intervalId);
    if (element) {
        element.textContent = finalText;
    }
}

/**
 * Shows notification message
 * @param {string} message - Message to display
 * @param {string} type - Message type ('success', 'error', 'warning')
 * @param {HTMLElement} container - Container element to append message to
 * @param {number} duration - Duration in ms to show message (default: 5000)
 */
export function showNotification(message, type = 'success', container, duration = 5000) {
    // Remove existing messages
    const existingMessages = container.querySelectorAll('.notification-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message element
    const messageElement = document.createElement('div');
    messageElement.className = `notification-message ${type}-message`;
    messageElement.textContent = message;
    
    // Add to container
    container.appendChild(messageElement);
    
    // Auto-remove after duration
    setTimeout(() => {
        if (messageElement.parentNode) {
            messageElement.remove();
        }
    }, duration);
}

/**
 * Debounce function to limit rapid function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

# SecureBank - Banking Web Application

A comprehensive banking web application built with vanilla JavaScript, HTML, and CSS. This application demonstrates modern web development practices with proper separation of concerns, asynchronous operations, and a responsive user interface.

## 🏦 Features

### Core Banking Features
- **Secure Login System** with demo credentials
- **Real-time Balance Display** with color-coded status indicators
- **Deposit Transactions** with validation and confirmation
- **Withdrawal Transactions** with insufficient funds protection
- **Transaction History** with detailed records and timestamps
- **Session Management** with automatic session restoration

### User Experience Features
- **Loading Animations** during transaction processing
- **Form Validation** with real-time feedback
- **Responsive Design** for mobile and desktop
- **Error Handling** with user-friendly messages
- **Keyboard Shortcuts** for improved accessibility
- **Auto-logout Protection** with confirmation dialogs

### Technical Features
- **Modular Architecture** with ES6 modules
- **Async/Await Operations** for realistic backend simulation
- **Local Storage Persistence** for session management
- **Input Sanitization** and validation
- **Progressive Enhancement** with graceful degradation

## 🚀 Getting Started

### Prerequisites
- Modern web browser with ES6 module support
- Local web server (recommended for development)

### Installation

1. **Clone or download** the project files to your local machine

2. **Serve the files** using a local web server:
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js (http-server)
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```

3. **Open your browser** and navigate to `http://localhost:8000`

### Demo Credentials

The application includes three demo accounts for testing:

| Username | Password    | Initial Balance | Account Holder |
|----------|-------------|-----------------|----------------|
| `demo`   | `password123` | $1,000.00      | John Doe       |
| `admin`  | `admin123`    | $5,000.00      | Admin User     |
| `user`   | `user123`     | $750.00        | Jane Smith     |

## 📁 Project Structure

```
banking-app/
├── index.html              # Main HTML structure
├── styles.css              # Complete CSS styling
├── js/                     # JavaScript modules
│   ├── main.js            # Application entry point
│   ├── auth.js            # Authentication logic
│   ├── dashboard.js       # Dashboard and transactions
│   └── utils.js           # Shared utility functions
└── README.md              # This file
```

## 🏗️ Architecture Overview

### HTML Structure
- **Semantic HTML5** with proper accessibility attributes
- **Responsive layout** with mobile-first approach
- **Form validation** with built-in HTML5 constraints
- **Loading overlays** for better user feedback

### CSS Styling
- **Modern CSS3** with flexbox and grid layouts
- **CSS animations** for loading states and transitions
- **Color-coded balance** indicators (low/medium/high)
- **Responsive design** with media queries
- **Custom properties** for consistent theming

### JavaScript Modules

#### `main.js` - Application Controller
- Application initialization and coordination
- Event listener setup and management
- View switching between login and dashboard
- Error handling and user feedback
- Keyboard shortcuts and accessibility features

#### `auth.js` - Authentication Module
- User login and logout functionality
- Session management with localStorage
- Credential validation and security
- Demo user account management
- Session restoration on page reload

#### `dashboard.js` - Dashboard Module
- Balance display and real-time updates
- Transaction processing (deposits/withdrawals)
- Transaction history management
- Form handling with loading states
- Balance color coding and status updates

#### `utils.js` - Utility Functions
- Amount validation with comprehensive checks
- Async delay simulation for realistic UX
- Currency formatting and display
- Date/time formatting for transactions
- Loading animations with setInterval
- Notification system for user feedback

## 💡 Key Implementation Details

### Asynchronous Operations
All transactions use `async/await` with simulated delays:

```javascript
// Simulates backend API call with random delay
export function delay(ms = null) {
    const delayTime = ms || Math.floor(Math.random() * 2000) + 1000;
    return new Promise(resolve => setTimeout(resolve, delayTime));
}
```

### Form Validation
Comprehensive validation for transaction amounts:

```javascript
export function validateAmount(amount, currentBalance = 0, type = 'deposit') {
    // Validates positive numbers, decimal places, limits, and balance
    // Returns {isValid: boolean, message: string}
}
```

### Loading Animations
Dynamic loading indicators using `setInterval()`:

```javascript
export function showLoadingAnimation(element, baseText = 'Processing') {
    let dots = 0;
    const intervalId = setInterval(() => {
        dots = (dots + 1) % 4;
        element.textContent = baseText + '.'.repeat(dots);
    }, 500);
    return intervalId;
}
```

### Balance Color Coding
Automatic balance status with visual indicators:

```javascript
export function getBalanceStatus(balance) {
    if (balance < 100) return {level: 'low', class: 'balance-low'};
    if (balance < 1000) return {level: 'medium', class: 'balance-medium'};
    return {level: 'high', class: 'balance-high'};
}
```

## 🔒 Security Features

- **Input Validation**: All user inputs are validated and sanitized
- **Session Management**: Secure session handling with expiration
- **XSS Protection**: Proper content escaping and validation
- **CSRF Prevention**: Form-based protection mechanisms
- **Error Handling**: Secure error messages without information leakage

## 📱 Responsive Design

The application is fully responsive with:
- **Mobile-first approach** with progressive enhancement
- **Flexible grid layouts** that adapt to screen sizes
- **Touch-friendly interfaces** with appropriate button sizes
- **Readable typography** across all devices
- **Optimized forms** for mobile input

## 🎨 User Interface Features

### Visual Feedback
- **Loading spinners** during async operations
- **Success/error messages** with auto-dismiss
- **Balance animations** on updates
- **Hover effects** for interactive elements
- **Color-coded status** indicators

### Accessibility
- **Keyboard navigation** support
- **Screen reader** compatible markup
- **High contrast** color schemes
- **Focus indicators** for form elements
- **ARIA labels** where appropriate

## 🧪 Testing the Application

### Manual Testing Checklist

1. **Login Functionality**
   - [ ] Valid credentials allow login
   - [ ] Invalid credentials show error
   - [ ] Loading state displays during login
   - [ ] Session persists on page refresh

2. **Transaction Processing**
   - [ ] Deposits increase balance correctly
   - [ ] Withdrawals decrease balance correctly
   - [ ] Insufficient funds prevents withdrawal
   - [ ] Loading animations work during processing
   - [ ] Transaction history updates immediately

3. **Form Validation**
   - [ ] Negative amounts are rejected
   - [ ] Zero amounts are rejected
   - [ ] Excessive decimal places are handled
   - [ ] Maximum transaction limits enforced

4. **User Interface**
   - [ ] Responsive design works on mobile
   - [ ] Balance colors change appropriately
   - [ ] Error messages display correctly
   - [ ] Logout functionality works

### Browser Compatibility
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 🔧 Customization

### Adding New Features
The modular architecture makes it easy to extend:

1. **New Transaction Types**: Add to `dashboard.js`
2. **Additional Validation**: Extend `utils.js`
3. **UI Components**: Update `styles.css`
4. **Authentication Methods**: Modify `auth.js`

### Configuration Options
Key settings can be modified in the respective modules:

- **Transaction Limits**: `utils.js` - `MAX_TRANSACTION`
- **Session Duration**: `auth.js` - session validation logic
- **Animation Timing**: `styles.css` - animation durations
- **Demo Users**: `auth.js` - `DEMO_USERS` object

## 📈 Performance Considerations

- **Lazy Loading**: Modules loaded only when needed
- **Event Delegation**: Efficient event handling
- **Memory Management**: Proper cleanup of intervals and timeouts
- **Minimal DOM Manipulation**: Efficient updates and caching
- **Optimized CSS**: Hardware-accelerated animations

## 🐛 Troubleshooting

### Common Issues

1. **Module Loading Errors**
   - Ensure files are served via HTTP/HTTPS (not file://)
   - Check browser console for specific error messages

2. **Session Not Persisting**
   - Verify localStorage is enabled in browser
   - Check for private/incognito mode restrictions

3. **Styling Issues**
   - Confirm CSS file is loading correctly
   - Check for browser cache issues

4. **JavaScript Errors**
   - Open browser developer tools
   - Check console for error messages
   - Verify all files are accessible

## 🤝 Contributing

This is a demonstration project, but improvements are welcome:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Built with vanilla JavaScript for educational purposes
- Inspired by modern banking interfaces
- Uses semantic HTML5 and modern CSS3 features
- Follows web accessibility guidelines (WCAG 2.1)

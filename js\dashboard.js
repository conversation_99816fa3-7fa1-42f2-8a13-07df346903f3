/**
 * Dashboard module for the banking application
 * Handles balance display, transactions, and transaction history
 */

import { 
    validateAmount, 
    delay, 
    formatCurrency, 
    formatDateTime, 
    generateTransactionId,
    getBalanceStatus,
    showLoadingAnimation,
    clearLoadingAnimation,
    showNotification 
} from './utils.js';
import { getCurrentUser, updateCurrentUser } from './auth.js';

/**
 * Updates the balance display with animation and color coding
 * @param {number} newBalance - New balance to display
 */
export function updateBalanceDisplay(newBalance) {
    const balanceElement = document.getElementById('current-balance');
    const balanceCard = document.querySelector('.balance-card');
    const statusElement = document.getElementById('balance-status-text');

    if (!balanceElement || !balanceCard || !statusElement) {
        console.error('Balance display elements not found');
        return;
    }

    // Add update animation
    balanceElement.classList.add('balance-updated');
    
    // Update balance text
    balanceElement.textContent = parseFloat(newBalance).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });

    // Update balance status and color
    const status = getBalanceStatus(newBalance);
    statusElement.textContent = status.text;
    
    // Remove existing balance classes
    balanceCard.classList.remove('balance-low', 'balance-medium', 'balance-high');
    
    // Add new balance class
    balanceCard.classList.add(status.class);

    // Remove animation class after animation completes
    setTimeout(() => {
        balanceElement.classList.remove('balance-updated');
    }, 500);
}

/**
 * Processes a deposit transaction
 * @param {number} amount - Amount to deposit
 * @returns {Promise<Object>} Transaction result
 */
export async function processDeposit(amount) {
    try {
        const user = getCurrentUser();
        if (!user) {
            throw new Error('User not logged in');
        }

        // Validate amount
        const validation = validateAmount(amount, user.balance, 'deposit');
        if (!validation.isValid) {
            return {
                success: false,
                message: validation.message
            };
        }

        // Simulate processing delay
        await delay();

        // Process transaction
        const depositAmount = parseFloat(amount);
        const newBalance = user.balance + depositAmount;
        
        // Create transaction record
        const transaction = {
            id: generateTransactionId(),
            type: 'deposit',
            amount: depositAmount,
            balance: newBalance,
            timestamp: new Date(),
            description: 'Cash Deposit'
        };

        // Update user data
        user.balance = newBalance;
        user.transactions.unshift(transaction); // Add to beginning of array
        updateCurrentUser(user);

        return {
            success: true,
            message: `Successfully deposited ${formatCurrency(depositAmount)}`,
            transaction,
            newBalance
        };

    } catch (error) {
        console.error('Deposit error:', error);
        return {
            success: false,
            message: 'An error occurred while processing the deposit. Please try again.'
        };
    }
}

/**
 * Processes a withdrawal transaction
 * @param {number} amount - Amount to withdraw
 * @returns {Promise<Object>} Transaction result
 */
export async function processWithdrawal(amount) {
    try {
        const user = getCurrentUser();
        if (!user) {
            throw new Error('User not logged in');
        }

        // Validate amount
        const validation = validateAmount(amount, user.balance, 'withdraw');
        if (!validation.isValid) {
            return {
                success: false,
                message: validation.message
            };
        }

        // Simulate processing delay
        await delay();

        // Process transaction
        const withdrawAmount = parseFloat(amount);
        const newBalance = user.balance - withdrawAmount;
        
        // Create transaction record
        const transaction = {
            id: generateTransactionId(),
            type: 'withdraw',
            amount: withdrawAmount,
            balance: newBalance,
            timestamp: new Date(),
            description: 'Cash Withdrawal'
        };

        // Update user data
        user.balance = newBalance;
        user.transactions.unshift(transaction); // Add to beginning of array
        updateCurrentUser(user);

        return {
            success: true,
            message: `Successfully withdrew ${formatCurrency(withdrawAmount)}`,
            transaction,
            newBalance
        };

    } catch (error) {
        console.error('Withdrawal error:', error);
        return {
            success: false,
            message: 'An error occurred while processing the withdrawal. Please try again.'
        };
    }
}

/**
 * Handles deposit form submission
 * @param {Event} event - Form submit event
 */
export async function handleDepositForm(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const amount = formData.get('amount');

    // Get form elements
    const submitButton = form.querySelector('button[type="submit"]');
    const amountInput = form.querySelector('input[name="amount"]');
    const btnText = submitButton.querySelector('.btn-text');
    const btnLoading = submitButton.querySelector('.btn-loading');
    const messagesContainer = document.getElementById('transaction-messages');

    // Show loading state
    submitButton.disabled = true;
    submitButton.classList.add('loading');
    btnText.classList.add('hidden');
    btnLoading.classList.remove('hidden');

    // Show global loading overlay
    const loadingOverlay = document.getElementById('loading-overlay');
    loadingOverlay.classList.remove('hidden');

    try {
        const result = await processDeposit(amount);

        if (result.success) {
            // Update balance display
            updateBalanceDisplay(result.newBalance);
            
            // Update transaction history
            updateTransactionHistory();
            
            // Show success message
            showNotification(result.message, 'success', messagesContainer);
            
            // Clear form
            form.reset();
            
        } else {
            // Show error message
            showNotification(result.message, 'error', messagesContainer);
        }

    } catch (error) {
        console.error('Deposit form error:', error);
        showNotification('An unexpected error occurred. Please try again.', 'error', messagesContainer);

    } finally {
        // Reset loading state
        submitButton.disabled = false;
        submitButton.classList.remove('loading');
        btnText.classList.remove('hidden');
        btnLoading.classList.add('hidden');
        
        // Hide global loading overlay
        loadingOverlay.classList.add('hidden');
    }
}

/**
 * Handles withdrawal form submission
 * @param {Event} event - Form submit event
 */
export async function handleWithdrawalForm(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const amount = formData.get('amount');

    // Get form elements
    const submitButton = form.querySelector('button[type="submit"]');
    const amountInput = form.querySelector('input[name="amount"]');
    const btnText = submitButton.querySelector('.btn-text');
    const btnLoading = submitButton.querySelector('.btn-loading');
    const messagesContainer = document.getElementById('transaction-messages');

    // Show loading state
    submitButton.disabled = true;
    submitButton.classList.add('loading');
    btnText.classList.add('hidden');
    btnLoading.classList.remove('hidden');

    // Show global loading overlay
    const loadingOverlay = document.getElementById('loading-overlay');
    loadingOverlay.classList.remove('hidden');

    try {
        const result = await processWithdrawal(amount);

        if (result.success) {
            // Update balance display
            updateBalanceDisplay(result.newBalance);
            
            // Update transaction history
            updateTransactionHistory();
            
            // Show success message
            showNotification(result.message, 'success', messagesContainer);
            
            // Clear form
            form.reset();
            
        } else {
            // Show error message
            showNotification(result.message, 'error', messagesContainer);
        }

    } catch (error) {
        console.error('Withdrawal form error:', error);
        showNotification('An unexpected error occurred. Please try again.', 'error', messagesContainer);

    } finally {
        // Reset loading state
        submitButton.disabled = false;
        submitButton.classList.remove('loading');
        btnText.classList.remove('hidden');
        btnLoading.classList.add('hidden');
        
        // Hide global loading overlay
        loadingOverlay.classList.add('hidden');
    }
}

/**
 * Updates the transaction history display
 */
export function updateTransactionHistory() {
    const user = getCurrentUser();
    const historyContainer = document.getElementById('transaction-history');

    if (!user || !historyContainer) {
        return;
    }

    // Clear existing content
    historyContainer.innerHTML = '';

    if (!user.transactions || user.transactions.length === 0) {
        historyContainer.innerHTML = '<p class="no-transactions">No transactions yet. Start by making a deposit or withdrawal.</p>';
        return;
    }

    // Create transaction items
    user.transactions.forEach(transaction => {
        const transactionElement = createTransactionElement(transaction);
        historyContainer.appendChild(transactionElement);
    });
}

/**
 * Creates a transaction history element
 * @param {Object} transaction - Transaction data
 * @returns {HTMLElement} Transaction element
 */
function createTransactionElement(transaction) {
    const transactionDiv = document.createElement('div');
    transactionDiv.className = 'transaction-item';

    const isDeposit = transaction.type === 'deposit';
    const icon = isDeposit ? '💰' : '💸';
    const amountClass = isDeposit ? 'amount-positive' : 'amount-negative';
    const amountPrefix = isDeposit ? '+' : '-';

    transactionDiv.innerHTML = `
        <div class="transaction-info">
            <div class="transaction-icon">${icon}</div>
            <div class="transaction-details">
                <h4>${transaction.description}</h4>
                <p>${formatDateTime(new Date(transaction.timestamp))}</p>
                <p>Transaction ID: ${transaction.id}</p>
            </div>
        </div>
        <div class="transaction-amount ${amountClass}">
            ${amountPrefix}${formatCurrency(transaction.amount, '')}
        </div>
    `;

    return transactionDiv;
}

/**
 * Initializes the dashboard with user data
 * @param {Object} user - User data
 */
export function initializeDashboard(user) {
    // Update welcome message
    const welcomeMessage = document.getElementById('welcome-message');
    if (welcomeMessage) {
        welcomeMessage.textContent = `Welcome, ${user.name}!`;
    }

    // Update balance display
    updateBalanceDisplay(user.balance);

    // Update transaction history
    updateTransactionHistory();

    // Set up real-time balance validation for withdrawal
    const withdrawInput = document.getElementById('withdraw-amount');
    if (withdrawInput) {
        withdrawInput.addEventListener('input', (e) => {
            const amount = parseFloat(e.target.value);
            if (!isNaN(amount) && amount > user.balance) {
                e.target.setCustomValidity('Amount exceeds available balance');
            } else {
                e.target.setCustomValidity('');
            }
        });
    }
}

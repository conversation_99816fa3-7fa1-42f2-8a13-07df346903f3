/**
 * Main application entry point
 * Coordinates all modules and handles application initialization
 */

import { 
    handleLoginForm, 
    logout, 
    isLoggedIn, 
    getCurrentUser, 
    restoreSession 
} from './auth.js';

import { 
    handleDepositForm, 
    handleWithdrawalForm, 
    initializeDashboard,
    updateBalanceDisplay,
    updateTransactionHistory
} from './dashboard.js';

import { showNotification } from './utils.js';

/**
 * Application state
 */
const AppState = {
    isInitialized: false,
    currentView: 'login' // 'login' or 'dashboard'
};

/**
 * DOM elements cache
 */
const Elements = {
    loginSection: null,
    dashboardSection: null,
    loginForm: null,
    depositForm: null,
    withdrawForm: null,
    logoutBtn: null,
    transactionMessages: null
};

/**
 * Initializes the application
 */
function initializeApp() {
    console.log('Initializing Banking Application...');

    // Cache DOM elements
    cacheElements();

    // Check for existing session
    if (restoreSession()) {
        console.log('Session restored');
        showDashboard();
    } else {
        console.log('No valid session found');
        showLogin();
    }

    // Set up event listeners
    setupEventListeners();

    // Mark as initialized
    AppState.isInitialized = true;
    console.log('Application initialized successfully');
}

/**
 * Caches frequently used DOM elements
 */
function cacheElements() {
    Elements.loginSection = document.getElementById('login-section');
    Elements.dashboardSection = document.getElementById('dashboard-section');
    Elements.loginForm = document.getElementById('login-form');
    Elements.depositForm = document.getElementById('deposit-form');
    Elements.withdrawForm = document.getElementById('withdraw-form');
    Elements.logoutBtn = document.getElementById('logout-btn');
    Elements.transactionMessages = document.getElementById('transaction-messages');

    // Validate critical elements
    const criticalElements = [
        'loginSection', 'dashboardSection', 'loginForm', 
        'depositForm', 'withdrawForm', 'logoutBtn'
    ];

    const missingElements = criticalElements.filter(key => !Elements[key]);
    
    if (missingElements.length > 0) {
        console.error('Missing critical DOM elements:', missingElements);
        showError('Application initialization failed. Please refresh the page.');
        return false;
    }

    return true;
}

/**
 * Sets up all event listeners
 */
function setupEventListeners() {
    // Login form submission
    Elements.loginForm.addEventListener('submit', async (event) => {
        await handleLoginForm(event, onLoginSuccess, onLoginError);
    });

    // Deposit form submission
    Elements.depositForm.addEventListener('submit', handleDepositForm);

    // Withdrawal form submission
    Elements.withdrawForm.addEventListener('submit', handleWithdrawalForm);

    // Logout button
    Elements.logoutBtn.addEventListener('click', handleLogout);

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Window beforeunload (warn about unsaved changes)
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Handle browser back/forward buttons
    window.addEventListener('popstate', handlePopState);

    console.log('Event listeners set up successfully');
}

/**
 * Handles successful login
 * @param {Object} user - User data
 */
function onLoginSuccess(user) {
    console.log('Login successful for user:', user.name);
    showDashboard();
    
    // Show welcome notification
    if (Elements.transactionMessages) {
        showNotification(
            `Welcome back, ${user.name}! Your current balance is $${user.balance.toFixed(2)}`,
            'success',
            Elements.transactionMessages,
            3000
        );
    }
}

/**
 * Handles login errors
 * @param {string} message - Error message
 */
function onLoginError(message) {
    console.log('Login failed:', message);
    // Error is already displayed by the auth module
}

/**
 * Shows the login view
 */
function showLogin() {
    Elements.loginSection.classList.remove('hidden');
    Elements.dashboardSection.classList.add('hidden');
    AppState.currentView = 'login';
    
    // Focus on username field
    const usernameField = document.getElementById('username');
    if (usernameField) {
        setTimeout(() => usernameField.focus(), 100);
    }
    
    console.log('Showing login view');
}

/**
 * Shows the dashboard view
 */
function showDashboard() {
    const user = getCurrentUser();
    
    if (!user) {
        console.error('Cannot show dashboard: no user logged in');
        showLogin();
        return;
    }

    Elements.loginSection.classList.add('hidden');
    Elements.dashboardSection.classList.remove('hidden');
    AppState.currentView = 'dashboard';
    
    // Initialize dashboard with user data
    initializeDashboard(user);
    
    console.log('Showing dashboard view for user:', user.name);
}

/**
 * Handles logout process
 */
async function handleLogout() {
    try {
        // Show confirmation dialog
        const confirmLogout = confirm('Are you sure you want to logout?');
        if (!confirmLogout) {
            return;
        }

        // Disable logout button during process
        Elements.logoutBtn.disabled = true;
        Elements.logoutBtn.textContent = 'Logging out...';

        // Perform logout
        const result = await logout();

        if (result.success) {
            console.log('Logout successful');
            showLogin();
            
            // Clear any existing messages
            if (Elements.transactionMessages) {
                Elements.transactionMessages.innerHTML = '';
            }
            
            // Show logout message
            const loginError = document.getElementById('login-error');
            if (loginError) {
                loginError.textContent = 'You have been logged out successfully.';
                loginError.className = 'success-message';
                loginError.classList.remove('hidden');
                
                // Hide message after 3 seconds
                setTimeout(() => {
                    loginError.classList.add('hidden');
                }, 3000);
            }
        } else {
            console.error('Logout failed:', result.message);
            alert('Logout failed. Please try again.');
        }

    } catch (error) {
        console.error('Logout error:', error);
        alert('An error occurred during logout. Please refresh the page.');
        
    } finally {
        // Reset logout button
        Elements.logoutBtn.disabled = false;
        Elements.logoutBtn.textContent = 'Logout';
    }
}

/**
 * Handles keyboard shortcuts
 * @param {KeyboardEvent} event - Keyboard event
 */
function handleKeyboardShortcuts(event) {
    // Only handle shortcuts when not typing in input fields
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    // Ctrl/Cmd + L: Focus on login (when on login page)
    if ((event.ctrlKey || event.metaKey) && event.key === 'l' && AppState.currentView === 'login') {
        event.preventDefault();
        const usernameField = document.getElementById('username');
        if (usernameField) {
            usernameField.focus();
        }
    }

    // Escape: Logout (when on dashboard)
    if (event.key === 'Escape' && AppState.currentView === 'dashboard') {
        event.preventDefault();
        handleLogout();
    }
}

/**
 * Handles browser beforeunload event
 * @param {BeforeUnloadEvent} event - Beforeunload event
 */
function handleBeforeUnload(event) {
    // Only warn if user is logged in
    if (isLoggedIn()) {
        event.preventDefault();
        event.returnValue = 'You are currently logged in. Are you sure you want to leave?';
        return event.returnValue;
    }
}

/**
 * Handles browser back/forward navigation
 * @param {PopStateEvent} event - Popstate event
 */
function handlePopState(event) {
    // Prevent navigation away from the app
    if (isLoggedIn()) {
        history.pushState(null, null, location.href);
    }
}

/**
 * Shows application error
 * @param {string} message - Error message
 */
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'app-error';
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #e74c3c;
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        z-index: 9999;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    `;
    errorDiv.innerHTML = `
        <h3>Application Error</h3>
        <p>${message}</p>
        <button onclick="location.reload()" style="
            background: white;
            color: #e74c3c;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 15px;
            cursor: pointer;
            font-weight: bold;
        ">Reload Page</button>
    `;
    
    document.body.appendChild(errorDiv);
}

/**
 * Application error handler
 */
window.addEventListener('error', (event) => {
    console.error('Application error:', event.error);
    showError('An unexpected error occurred. Please reload the page.');
});

/**
 * Unhandled promise rejection handler
 */
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    showError('An unexpected error occurred. Please reload the page.');
});

// Initialize the application when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM is already loaded
    initializeApp();
}

// Export for debugging purposes
window.BankingApp = {
    AppState,
    Elements,
    getCurrentUser,
    isLoggedIn,
    showLogin,
    showDashboard
};

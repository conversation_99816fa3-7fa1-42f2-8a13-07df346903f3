/**
 * Authentication module for the banking application
 * Handles user login, logout, and session management
 */

import { delay, showNotification } from './utils.js';

// Demo user credentials (in a real app, this would be handled by a backend)
const DEMO_USERS = {
    'demo': {
        password: 'password123',
        name: '<PERSON>',
        accountNumber: '****1234',
        initialBalance: 1000.00
    },
    'admin': {
        password: 'admin123',
        name: 'Admin User',
        accountNumber: '****5678',
        initialBalance: 5000.00
    },
    'user': {
        password: 'user123',
        name: '<PERSON>',
        accountNumber: '****9012',
        initialBalance: 750.00
    }
};

/**
 * Current user session data
 */
let currentUser = null;

/**
 * Validates user credentials
 * @param {string} username - Username to validate
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with isValid, user, and message properties
 */
function validateCredentials(username, password) {
    // Check if username exists
    if (!DEMO_USERS[username]) {
        return {
            isValid: false,
            user: null,
            message: 'Invalid username or password'
        };
    }

    // Check password
    if (DEMO_USERS[username].password !== password) {
        return {
            isValid: false,
            user: null,
            message: 'Invalid username or password'
        };
    }

    return {
        isValid: true,
        user: DEMO_USERS[username],
        message: 'Login successful'
    };
}

/**
 * Performs user login with async simulation
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Promise<Object>} Login result
 */
export async function login(username, password) {
    try {
        // Input validation
        if (!username || !password) {
            return {
                success: false,
                message: 'Please enter both username and password'
            };
        }

        if (username.trim().length < 2) {
            return {
                success: false,
                message: 'Username must be at least 2 characters long'
            };
        }

        if (password.length < 6) {
            return {
                success: false,
                message: 'Password must be at least 6 characters long'
            };
        }

        // Simulate network delay
        await delay(1500);

        // Validate credentials
        const validation = validateCredentials(username.trim(), password);

        if (!validation.isValid) {
            return {
                success: false,
                message: validation.message
            };
        }

        // Set current user session
        currentUser = {
            username: username.trim(),
            name: validation.user.name,
            accountNumber: validation.user.accountNumber,
            balance: validation.user.initialBalance,
            loginTime: new Date(),
            transactions: []
        };

        // Store session in localStorage for persistence
        localStorage.setItem('bankingSession', JSON.stringify({
            username: currentUser.username,
            name: currentUser.name,
            accountNumber: currentUser.accountNumber,
            balance: currentUser.balance,
            loginTime: currentUser.loginTime.toISOString(),
            transactions: currentUser.transactions
        }));

        return {
            success: true,
            message: 'Login successful',
            user: currentUser
        };

    } catch (error) {
        console.error('Login error:', error);
        return {
            success: false,
            message: 'An error occurred during login. Please try again.'
        };
    }
}

/**
 * Logs out the current user
 * @returns {Promise<Object>} Logout result
 */
export async function logout() {
    try {
        // Simulate logout delay
        await delay(500);

        // Clear current user session
        currentUser = null;

        // Clear localStorage
        localStorage.removeItem('bankingSession');

        return {
            success: true,
            message: 'Logged out successfully'
        };

    } catch (error) {
        console.error('Logout error:', error);
        return {
            success: false,
            message: 'An error occurred during logout'
        };
    }
}

/**
 * Checks if user is currently logged in
 * @returns {boolean} True if user is logged in
 */
export function isLoggedIn() {
    return currentUser !== null;
}

/**
 * Gets current user data
 * @returns {Object|null} Current user data or null if not logged in
 */
export function getCurrentUser() {
    return currentUser;
}

/**
 * Restores user session from localStorage
 * @returns {boolean} True if session was restored successfully
 */
export function restoreSession() {
    try {
        const sessionData = localStorage.getItem('bankingSession');
        
        if (!sessionData) {
            return false;
        }

        const session = JSON.parse(sessionData);
        
        // Check if session is still valid (e.g., not older than 24 hours)
        const loginTime = new Date(session.loginTime);
        const now = new Date();
        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
        
        if (hoursDiff > 24) {
            // Session expired
            localStorage.removeItem('bankingSession');
            return false;
        }

        // Restore session
        currentUser = {
            username: session.username,
            name: session.name,
            accountNumber: session.accountNumber,
            balance: session.balance,
            loginTime: loginTime,
            transactions: session.transactions || []
        };

        return true;

    } catch (error) {
        console.error('Session restore error:', error);
        localStorage.removeItem('bankingSession');
        return false;
    }
}

/**
 * Updates current user data (for balance changes, etc.)
 * @param {Object} updates - Object containing fields to update
 */
export function updateCurrentUser(updates) {
    if (!currentUser) {
        throw new Error('No user is currently logged in');
    }

    // Update current user object
    Object.assign(currentUser, updates);

    // Update localStorage
    localStorage.setItem('bankingSession', JSON.stringify({
        username: currentUser.username,
        name: currentUser.name,
        accountNumber: currentUser.accountNumber,
        balance: currentUser.balance,
        loginTime: currentUser.loginTime.toISOString(),
        transactions: currentUser.transactions
    }));
}

/**
 * Handles login form submission
 * @param {Event} event - Form submit event
 * @param {Function} onSuccess - Callback function for successful login
 * @param {Function} onError - Callback function for login errors
 */
export async function handleLoginForm(event, onSuccess, onError) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const username = formData.get('username');
    const password = formData.get('password');

    // Get form elements
    const submitButton = form.querySelector('button[type="submit"]');
    const errorElement = form.querySelector('#login-error');
    const btnText = submitButton.querySelector('.btn-text');
    const btnLoading = submitButton.querySelector('.btn-loading');

    // Show loading state
    submitButton.disabled = true;
    submitButton.classList.add('loading');
    btnText.classList.add('hidden');
    btnLoading.classList.remove('hidden');
    errorElement.classList.add('hidden');

    try {
        const result = await login(username, password);

        if (result.success) {
            if (onSuccess) {
                onSuccess(result.user);
            }
        } else {
            errorElement.textContent = result.message;
            errorElement.classList.remove('hidden');
            
            if (onError) {
                onError(result.message);
            }
        }

    } catch (error) {
        const errorMessage = 'An unexpected error occurred. Please try again.';
        errorElement.textContent = errorMessage;
        errorElement.classList.remove('hidden');
        
        if (onError) {
            onError(errorMessage);
        }

    } finally {
        // Reset loading state
        submitButton.disabled = false;
        submitButton.classList.remove('loading');
        btnText.classList.remove('hidden');
        btnLoading.classList.add('hidden');
    }
}

/**
 * Gets list of demo users for testing
 * @returns {Array} Array of demo user info
 */
export function getDemoUsers() {
    return Object.keys(DEMO_USERS).map(username => ({
        username,
        name: DEMO_USERS[username].name,
        accountNumber: DEMO_USERS[username].accountNumber
    }));
}

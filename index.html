<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureBank - Online Banking</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Processing transaction...</p>
        </div>
    </div>

    <!-- Login Section -->
    <div id="login-section" class="login-section">
        <div class="login-container">
            <div class="bank-logo">
                <h1>🏦 SecureBank</h1>
                <p>Your trusted banking partner</p>
            </div>
            
            <form id="login-form" class="login-form">
                <h2>Login to Your Account</h2>
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required 
                           placeholder="Enter your username" autocomplete="username">
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="Enter your password" autocomplete="current-password">
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-text">Login</span>
                    <span class="btn-loading hidden">Logging in...</span>
                </button>
                
                <div id="login-error" class="error-message hidden"></div>
            </form>
            
            <div class="demo-credentials">
                <h3>Demo Credentials</h3>
                <p><strong>Username:</strong> demo</p>
                <p><strong>Password:</strong> password123</p>
            </div>
        </div>
    </div>

    <!-- Dashboard Section -->
    <div id="dashboard-section" class="dashboard-section hidden">
        <header class="dashboard-header">
            <div class="header-content">
                <h1>🏦 SecureBank Dashboard</h1>
                <div class="user-info">
                    <span id="welcome-message">Welcome, User!</span>
                    <button id="logout-btn" class="logout-btn">Logout</button>
                </div>
            </div>
        </header>

        <main class="dashboard-main">
            <!-- Account Balance Card -->
            <div class="balance-card">
                <h2>Account Balance</h2>
                <div class="balance-display">
                    <span class="currency">$</span>
                    <span id="current-balance" class="balance-amount">1,000.00</span>
                </div>
                <div class="balance-status">
                    <span id="balance-status-text" class="status-text">Good Standing</span>
                </div>
            </div>

            <!-- Transaction Forms -->
            <div class="transaction-section">
                <div class="transaction-cards">
                    <!-- Deposit Card -->
                    <div class="transaction-card deposit-card">
                        <h3>💰 Deposit Money</h3>
                        <form id="deposit-form" class="transaction-form">
                            <div class="form-group">
                                <label for="deposit-amount">Amount</label>
                                <input type="number" id="deposit-amount" name="amount" 
                                       step="0.01" min="0.01" max="10000" required
                                       placeholder="Enter amount to deposit">
                            </div>
                            <button type="submit" class="transaction-btn deposit-btn">
                                <span class="btn-text">Deposit</span>
                                <span class="btn-loading hidden">Processing...</span>
                            </button>
                        </form>
                    </div>

                    <!-- Withdraw Card -->
                    <div class="transaction-card withdraw-card">
                        <h3>💸 Withdraw Money</h3>
                        <form id="withdraw-form" class="transaction-form">
                            <div class="form-group">
                                <label for="withdraw-amount">Amount</label>
                                <input type="number" id="withdraw-amount" name="amount" 
                                       step="0.01" min="0.01" required
                                       placeholder="Enter amount to withdraw">
                            </div>
                            <button type="submit" class="transaction-btn withdraw-btn">
                                <span class="btn-text">Withdraw</span>
                                <span class="btn-loading hidden">Processing...</span>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Transaction Messages -->
                <div id="transaction-messages" class="transaction-messages"></div>
            </div>

            <!-- Transaction History -->
            <div class="history-section">
                <h3>📊 Recent Transactions</h3>
                <div id="transaction-history" class="transaction-history">
                    <p class="no-transactions">No transactions yet. Start by making a deposit or withdrawal.</p>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript Modules -->
    <script type="module" src="js/main.js"></script>
</body>
</html>

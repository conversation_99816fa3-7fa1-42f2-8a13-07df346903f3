/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.hidden {
    display: none !important;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Login Section */
.login-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    text-align: center;
}

.bank-logo h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5em;
}

.bank-logo p {
    color: #7f8c8d;
    margin-bottom: 30px;
}

.login-form h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.login-btn, .transaction-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover, .transaction-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.login-btn:active, .transaction-btn:active {
    transform: translateY(0);
}

.demo-credentials {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: left;
}

.demo-credentials h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.demo-credentials p {
    margin: 5px 0;
    color: #555;
}

/* Error Messages */
.error-message {
    background: #e74c3c;
    color: white;
    padding: 10px;
    border-radius: 5px;
    margin-top: 15px;
    font-size: 14px;
}

.success-message {
    background: #27ae60;
    color: white;
    padding: 10px;
    border-radius: 5px;
    margin: 15px 0;
    font-size: 14px;
}

.warning-message {
    background: #f39c12;
    color: white;
    padding: 10px;
    border-radius: 5px;
    margin: 15px 0;
    font-size: 14px;
}

/* Dashboard Section */
.dashboard-section {
    min-height: 100vh;
    background: #f5f7fa;
}

.dashboard-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    color: #2c3e50;
    font-size: 1.8em;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logout-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #c0392b;
}

.dashboard-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

/* Balance Card */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.balance-card h2 {
    margin-bottom: 20px;
    font-size: 1.5em;
    opacity: 0.9;
}

.balance-display {
    font-size: 3em;
    font-weight: bold;
    margin-bottom: 15px;
}

.currency {
    font-size: 0.8em;
    opacity: 0.8;
}

.balance-status {
    font-size: 1.1em;
    opacity: 0.9;
}

/* Balance Color Coding */
.balance-low {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
}

.balance-medium {
    background: linear-gradient(135deg, #f39c12, #e67e22) !important;
}

.balance-high {
    background: linear-gradient(135deg, #27ae60, #229954) !important;
}

/* Transaction Section */
.transaction-section {
    margin-bottom: 30px;
}

.transaction-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.transaction-card {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.transaction-card h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.deposit-btn {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.deposit-btn:hover {
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.withdraw-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.withdraw-btn:hover {
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

/* Transaction History */
.history-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.history-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.transaction-history {
    max-height: 400px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #ecf0f1;
    transition: background 0.3s ease;
}

.transaction-item:hover {
    background: #f8f9fa;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.transaction-icon {
    font-size: 1.5em;
}

.transaction-details h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.transaction-details p {
    color: #7f8c8d;
    font-size: 0.9em;
}

.transaction-amount {
    font-weight: bold;
    font-size: 1.1em;
}

.amount-positive {
    color: #27ae60;
}

.amount-negative {
    color: #e74c3c;
}

.no-transactions {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 40px;
}

/* Button Loading States */
.btn-loading {
    display: none;
}

.loading .btn-text {
    display: none;
}

.loading .btn-loading {
    display: inline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .balance-display {
        font-size: 2.5em;
    }
    
    .transaction-cards {
        grid-template-columns: 1fr;
    }
    
    .login-container {
        padding: 30px 20px;
    }
}

/* Animation for balance updates */
@keyframes balanceUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.balance-updated {
    animation: balanceUpdate 0.5s ease-in-out;
}
